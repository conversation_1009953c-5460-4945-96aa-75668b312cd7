import { Config, ORM, ValidationLib } from "./core/config";
import { TemplateEngine } from "./core/TemplateEngine";
import { ProjectContext } from "../project-context/ProjectContext";
import { IModularGenerator } from "../project-context/generators";
import { Hook } from "../project-context/hooks";
import { ReadmeGenerator } from "./generators/readme/readme.generator";
import { EnvGenerator } from "./generators/env/env.generator";
import { TsConfigGenerator } from "./generators/tsconfig/tsconfig.generator";
import { PackageJsonGenerator } from "./generators/package-json/package-json.generator";
import { CoreGenerator } from "./generators/core/core.generator";
import { GitignoreGenerator } from "./generators/core/gitignore.generator";
import { ServerGenerator } from "./generators/server/server.generator";
import { PrismaDbGenerator } from "./generators/orm/prisma-db.generator";
import { PrismaGenerator } from "./generators/orm/prisma.generator";

import { TypeORMGenerator } from "./generators/orm/typeorm.generator";
import { CrudRoutesGenerator } from "./generators/crud/routes.generator";
import { CrudControllerGenerator } from "./generators/crud/controller.generator";
import { RepositoryGenerator } from "./generators/crud/repository.generator";
import { EnvValidationGenerator } from "./generators/env-validation/env-validation.generator";
import { RequestSchemaZodGenerator } from "./generators/validation/request-schema.zod.generator";

export class ProjectGenerator {
    private readonly config: Config;

    constructor(config: Config) {
        this.config = config;
    }

    public async generate(): Promise<void> {
        const projectContext = new ProjectContext(
            this.config,
            new TemplateEngine()
        );

        try {
            console.log(
                `Generating project files for ${this.config.projectName}...`
            );

            // 1. Invoke beforeInitialize hook
            await projectContext.invokeHook(Hook.BeforeInitialize);

            // 2. Invoke afterInitialize hook
            await projectContext.invokeHook(Hook.AfterInitialize);

            // 3. Invoke beforeGeneratorRegistration hook
            await projectContext.invokeHook(Hook.BeforeGeneratorRegistration);

            const generators: IModularGenerator[] = [
                new ReadmeGenerator(),
                new EnvGenerator(projectContext),
                new TsConfigGenerator(),
                new PackageJsonGenerator(),
                new CoreGenerator(projectContext),
                new GitignoreGenerator(projectContext),
                new ServerGenerator(),
                new EnvValidationGenerator(projectContext),
            ];

            if (this.config.orm === ORM.Prisma) {
                generators.push(new PrismaDbGenerator(projectContext));
                generators.push(new PrismaGenerator(projectContext));
            } else if (this.config.orm === ORM.TypeORM) {
                generators.push(new TypeORMGenerator(projectContext));
            }

            if (this.config.includeCrudRoutes) {
                generators.push(new CrudRoutesGenerator(projectContext));
                generators.push(new CrudControllerGenerator(projectContext));
                generators.push(new RepositoryGenerator(projectContext));
            }

            if (this.config.validation === ValidationLib.Zod) {
                generators.push(new RequestSchemaZodGenerator(projectContext));
            }

            // Register generators (if they have a register method)
            for (const generator of generators) {
                if (generator.register) {
                    await generator.register(projectContext);
                }
            }

            // 4. Invoke afterGeneratorRegistration hook
            await projectContext.invokeHook(Hook.AfterGeneratorRegistration);

            // 5. Invoke beforeAllGenerators hook
            await projectContext.invokeHook(Hook.BeforeAllGenerators);

            // Run all generators
            for (const generator of generators) {
                // 6. Invoke beforeGenerator hook
                await projectContext.invokeHook(
                    Hook.BeforeGenerator,
                    generator
                );

                // Run generator's main logic
                await generator.generate(projectContext);

                // 7. Invoke afterGenerator hook
                await projectContext.invokeHook(Hook.AfterGenerator, generator);
            }

            // 8. Invoke beforeFinalize hook
            await projectContext.invokeHook(Hook.BeforeFinalize);

            // Finalize the project (e.g., write package.json, consolidate files)
            await projectContext.finalize();

            // 9. Invoke afterFinalize hook
            await projectContext.invokeHook(Hook.AfterFinalize);

            console.log(
                `Project files generated successfully for ${this.config.projectName}.`
            );
        } catch (error: any) {
            // Catch block with typed error
            // Check if projectContext was successfully initialized before invoking its hook
            if (projectContext) {
                await projectContext.invokeHook(
                    Hook.OnError,
                    error,
                    "generation-phase"
                );
            }
            console.error(`Project generation failed:`, error.message || error);
            throw error; // Re-throw to indicate failure
        }
    }
}
