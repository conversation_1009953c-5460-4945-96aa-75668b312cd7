import { z } from "zod/v4";

export enum PackageManager {
    NPM = "npm",
    Yarn = "yarn",
    PNPM = "pnpm",
}

export const PackageManagerSchema = z.enum([
    PackageManager.NPM,
    PackageManager.Yarn,
    PackageManager.PNPM,
]);

export enum ORM {
    Prisma = "prisma",
    Drizzle = "drizzle",
    Sequelize = "sequelize",
    TypeORM = "typeorm",
}

export const ORMSchema = z.enum([
    ORM.Prisma,
    ORM.Drizzle,
    ORM.Sequelize,
    ORM.TypeORM,
]);

export enum Database {
    PostgreSQL = "postgresql",
    MySQL = "mysql",
    SQLite = "sqlite",
}

export const DatabaseSchema = z.enum([
    Database.PostgreSQL,
    Database.MySQL,
    Database.SQLite,
]);

export enum AuthType {
    None = "none",
    JWT = "jwt",
}

export const AuthTypeSchema = z.enum([AuthType.None, AuthType.JWT]);

export enum ValidationLib {
    Zod = "zod",
    Joi = "joi",
    ClassValidator = "class-validator",
}

export const ValidationLibSchema = z.enum([
    ValidationLib.Zod,
    ValidationLib.Joi,
    ValidationLib.ClassValidator,
]);

import { Model, ModelDefinitionSchema } from "./definitions/model";

export const ProjectConfigurationSchema = z.object({
    projectName: z
        .string()
        .min(1)
        .regex(
            /^[a-zA-Z0-9_-]+$/,
            "Invalid directory name. Only letters, numbers, dashes, and underscores are allowed."
        ),
    projectDescription: z.string().optional(),
    packageManager: PackageManagerSchema,
    orm: ORMSchema,
    database: DatabaseSchema,
    auth: AuthTypeSchema,
    validation: ValidationLibSchema,
    models: z.array(ModelDefinitionSchema),
    includeCrudRoutes: z.boolean(),
});

export type ProjectConfiguration = z.infer<typeof ProjectConfigurationSchema>;

export class Config implements ProjectConfiguration {
    projectName: string;
    projectDescription?: string;
    packageManager: PackageManager;
    orm: ORM;
    database: Database;
    auth: AuthType;
    validation: ValidationLib;
    models: Model[];
    includeCrudRoutes: boolean;

    constructor(definition: ProjectConfiguration) {
        const parsedDefinition = ProjectConfigurationSchema.parse(definition);

        this.projectName = parsedDefinition.projectName;
        this.projectDescription = parsedDefinition.projectDescription;
        this.packageManager = parsedDefinition.packageManager;
        this.orm = parsedDefinition.orm;
        this.database = parsedDefinition.database;
        this.auth = parsedDefinition.auth;
        this.validation = parsedDefinition.validation;
        this.models = parsedDefinition.models.map(
            (modelDef) => new Model(modelDef)
        );
        this.includeCrudRoutes = parsedDefinition.includeCrudRoutes;
    }
}
