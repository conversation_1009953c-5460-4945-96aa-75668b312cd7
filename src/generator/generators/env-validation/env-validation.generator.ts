import { ValidationLib } from "../../core/config";
import path from "path";
import { Model } from "../../core/definitions/model";
import { Field, FieldType } from "../../core/definitions/field";
import { IModularGenerator } from "../../../project-context/generators";
import { ProjectContext } from "../../../project-context/ProjectContext";
import { ZodSchemaGenerator } from "../../core/ZodSchemaGenerator";
import { Hook } from "../../../project-context/hooks";
import { EnvVariable } from "../../../project-context/env-variable";

export class EnvValidationGenerator implements IModularGenerator {
    constructor(private projectContext: ProjectContext) {}

    async register?(): Promise<void> {
        this.projectContext.addDependency("chalk", "^5.3.0");

        const config = this.projectContext.projectConfiguration;
        if (config.validation === ValidationLib.Zod) {
            this.projectContext.addDependency("zod");
        }

        this.projectContext.registerHook(
            Hook.BeforeFinalize,
            this.generateEnvFile.bind(this)
        );
    }

    async generate(): Promise<void> {}

    private async generateEnvFile(): Promise<void> {
        const config = this.projectContext.projectConfiguration;
        const templateEngine = this.projectContext.getTemplateEngine();
        const fileSystem = this.projectContext.getFileSystem();

        if (config.validation !== ValidationLib.Zod) {
            return;
        }

        const envVariables: Map<string, EnvVariable> =
            this.projectContext.getEnvVariables();
        const envFields: Field[] = [];

        for (const [key, envVar] of envVariables.entries()) {
            let fieldType: FieldType = envVar.type || "string";
            let defaultValue = envVar.defaultValue;
            const isPrimaryKey = key === "NODE_ENV";

            envFields.push(
                new Field({
                    name: key,
                    type: fieldType,
                    isRequired: true, // All env vars are required for schema validation
                    isPrimaryKey: isPrimaryKey,
                    isUnique: false,
                    coerce: true,
                    defaultValue: defaultValue
                        ? { type: "static", value: defaultValue }
                        : undefined,
                    description: envVar.description,
                })
            );
        }

        const envSchema = new Model({
            hasEnumFields: false,
            name: "EnvConfig",
            fields: envFields,
            description:
                "Environment configuration schema for the application.",
        });

        const zodSchemaGenerator = new ZodSchemaGenerator(templateEngine);
        const schema = zodSchemaGenerator.generateSchemaString(envSchema, [
            envSchema,
        ]);

        const content = await templateEngine.renderTemplate(
            path.join(import.meta.dirname, "../../templates/lib/env.hbs"),
            { config, schema }
        );

        await fileSystem.writeFile("src/lib/env.ts", content.trim());
    }
}
