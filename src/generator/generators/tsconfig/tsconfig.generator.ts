import { ProjectContext } from "../../../project-context/ProjectContext";
import { IModularGenerator } from "../../../project-context/generators";
import { ORM } from "../../core/config";

export class TsConfigGenerator implements IModularGenerator {
    async generate(projectContext: ProjectContext): Promise<void> {
        const config = projectContext.projectConfiguration;
        const fileSystem = projectContext.getFileSystem();

        const tsconfigContent: any = {
            include: ["src/"],
            exclude: ["node_modules"],
            compilerOptions: {
                lib: ["ES2023"],
                module: "commonjs",
                target: "ES2022",
                moduleResolution: "node",
                strict: true,
                esModuleInterop: true,
                skipLibCheck: true,
                forceConsistentCasingInFileNames: true,
                outDir: "dist",
                rootDir: "src",
                resolveJsonModule: true,
            },
        };

        if (config.orm === ORM.TypeORM) {
            tsconfigContent.compilerOptions.experimentalDecorators = true;
            tsconfigContent.compilerOptions.emitDecoratorMetadata = true;
            tsconfigContent.compilerOptions.strictPropertyInitialization =
                false;
        }

        const content = JSON.stringify(tsconfigContent, null, 2);

        await fileSystem.writeFile("tsconfig.json", content + "\n");
    }
}
