import { DataSource } from 'typeorm';
import * as path from 'path';
import { env } from './env';
import chalk from "chalk";

export const AppDataSource = new DataSource({
    type: "{{ ormDbDriver }}",
    {{#if (ne ormDbDriver "sqlite")}}
    host: env.DB_HOST,
    port: env.DB_PORT,
    username: env.DB_USER,
    password: env.DB_PASSWORD,
    {{/if}}
    database: env.DB_NAME,
    synchronize: env.NODE_ENV === "development", // In production, this should be false and migrations should be used
    logging: env.NODE_ENV === "development",
    entities: [
        path.join(__dirname, '../**/*.entity.{ts,js}'),
    ],
    migrations: [],
    subscribers: [],
});

AppDataSource.initialize().then(() => {
    console.log(chalk.green("Database connection established."));
}).catch((error: Error) => {
    console.error(chalk.red("Database connection failed: ", error));
    process.exit(1)
})