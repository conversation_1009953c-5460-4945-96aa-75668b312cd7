{
  "name": "{{{config.projectName}}}",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    {{#each config.scripts as |value key|}}
    "{{{key}}}": "{{{value}}}"{{#unless @last}},{{/unless}}
    {{/each}}
  },
  "dependencies": {
    {{#each config.dependencies as |value key|}}
    "{{{key}}}": "{{{value}}}"{{#unless @last}},{{/unless}}
    {{/each}}
  },
  "devDependencies": {
    {{#each config.devDependencies as |value key|}}
    "{{{key}}}": "{{{value}}}"{{#unless @last}},{{/unless}}
    {{/each}}
  }
}
