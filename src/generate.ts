import { ProjectGenerator } from "./generator/ProjectGenerator";
import {
    AuthType,
    Database,
    ORM,
    PackageManager,
    ProjectConfiguration,
    ValidationLib,
    Config,
} from "./generator/core/config";

async function generateProject() {
    const config: ProjectConfiguration = {
        projectName: "MyProject",
        projectDescription: "A project generated for testing purposes.",
        packageManager: PackageManager.NPM,
        orm: ORM.TypeORM,
        database: Database.SQLite,
        auth: AuthType.JWT,
        validation: ValidationLib.Zod,
        models: [
            {
                name: "User",
                description: "User model for authentication",
                hasEnumFields: false,
                fields: [
                    {
                        name: "id",
                        type: "number",
                        isRequired: true,
                        isUnique: true,
                        isPrimaryKey: true,
                    },
                    {
                        name: "email",
                        type: "string",
                        isRequired: true,
                        isUnique: true,
                        isPrimaryKey: false,
                        validations: [{ rule: "email" }],
                    },
                    {
                        name: "password",
                        type: "string",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        validations: [
                            { rule: "minLength", value: 8 },
                            { rule: "maxLength", value: 255 },
                        ],
                    },
                    {
                        name: "createdAt",
                        type: "date",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        defaultValue: { type: "dynamic", value: "now" },
                    },
                    {
                        name: "updatedAt",
                        type: "date",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        defaultValue: { type: "dynamic", value: "now" },
                    },
                ],
            },
            {
                name: "Post",
                description: "Blog post model",
                hasEnumFields: false,
                fields: [
                    {
                        name: "id",
                        type: "number",
                        isRequired: true,
                        isUnique: true,
                        isPrimaryKey: true,
                    },
                    {
                        name: "title",
                        type: "string",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        validations: [
                            { rule: "minLength", value: 5 },
                            { rule: "maxLength", value: 100 },
                        ],
                    },
                    {
                        name: "content",
                        type: "string",
                        isRequired: false,
                        isUnique: false,
                        isPrimaryKey: false,
                    },
                    {
                        name: "published",
                        type: "boolean",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        defaultValue: { type: "static", value: false },
                    },
                    {
                        name: "author",
                        type: "relation",
                        relation: {
                            targetModel: "User",
                            type: "many-to-one",
                        },
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                    },
                    {
                        name: "createdAt",
                        type: "date",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        defaultValue: { type: "dynamic", value: "now" },
                    },
                ],
            },
            {
                name: "Product",
                description: "E-commerce product model",
                hasEnumFields: false,
                fields: [
                    {
                        name: "id",
                        type: "number",
                        isRequired: true,
                        isUnique: true,
                        isPrimaryKey: true,
                    },
                    {
                        name: "name",
                        type: "string",
                        isRequired: true,
                        isUnique: true,
                        isPrimaryKey: false,
                        validations: [
                            { rule: "minLength", value: 3 },
                            { rule: "maxLength", value: 200 },
                        ],
                    },
                    {
                        name: "price",
                        type: "number",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        validations: [{ rule: "min", value: 0.01 }],
                    },
                    {
                        name: "description",
                        type: "string",
                        isRequired: false,
                        isUnique: false,
                        isPrimaryKey: false,
                    },
                    {
                        name: "category",
                        type: "enum",
                        isRequired: true,
                        isUnique: false,
                        isPrimaryKey: false,
                        enumValues: [
                            "Electronics",
                            "Books",
                            "Clothing",
                            "Home",
                        ],
                    },
                    {
                        name: "sku",
                        type: "string",
                        isRequired: true,
                        isUnique: true,
                        isPrimaryKey: false,
                        defaultValue: { type: "dynamic", value: "uuid" },
                    },
                ],
            },
        ],
        includeCrudRoutes: true,
    };

    const generator = new ProjectGenerator(new Config(config));
    await generator.generate();
    console.log("Project generation completed successfully!");
}

generateProject();
