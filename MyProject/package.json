{"name": "MyProject", "version": "1.0.0", "private": true, "type": "module", "scripts": {"start": "node --watch dist/index.js", "dev": "tsx --watch --env-file=.env src/index.ts", "build": "tsc"}, "dependencies": {"chalk": "^5.3.0", "express": "latest", "reflect-metadata": "^0.2.2", "sqlite3": "latest", "typeorm": "latest", "zod": "latest"}, "devDependencies": {"@types/express": "latest", "@types/node": "latest", "ts-node": "^10.9.2", "tsx": "latest", "typescript": "latest"}}