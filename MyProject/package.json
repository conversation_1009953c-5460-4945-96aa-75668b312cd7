{"name": "MyProject", "version": "1.0.0", "private": true, "type": "module", "scripts": {"start": "node --watch dist/index.js", "dev": "tsx --watch --env-file=.env src/index.ts", "build": "tsc"}, "dependencies": {"express": "latest", "chalk": "^5.3.0", "zod": "latest", "typeorm": "latest", "sqlite3": "latest"}, "devDependencies": {"typescript": "latest", "@types/node": "latest", "@types/express": "latest", "tsx": "latest"}}