import "reflect-metadata";

import chalk from "chalk";

import express from "express";
import { env } from "./lib/env";

// middleware
import globalErrorHandler from "./middleware/global-error-handler";

// routes
import userRoutes from "./routes/user.routes";
import postRoutes from "./routes/post.routes";
import productRoutes from "./routes/product.routes";

const app = express();

app.use(express.json());

app.use("/api/users", userRoutes);
app.use("/api/posts", postRoutes);
app.use("/api/products", productRoutes);


// Register global error handler
app.use(globalErrorHandler);

app.listen(env.PORT, (error) => {
    if (error) {
        console.error(chalk.red("Server could not be started", error));
        process.exit(1);
    }
    console.log(chalk.bgGreen(`Server started on port ${env.PORT}`));
});