import { z } from "zod/v4";
import chalk from "chalk";

export const envSchema = z.object({
  NODE_ENV: z.coerce.string().default("development").describe("The environment the application is running in (development, production, test)"),
  PORT: z.coerce.number().default(3000).describe("The port the application listens on"),
  DB_NAME: z.coerce.string().default("db.sqlite")
}).describe("Environment configuration schema for the application.");

export type Env = z.infer<typeof envSchema>;

const parsedEnv = envSchema.safeParse(process.env);

if (!parsedEnv.success) {
  console.error(
    chalk.red.bold("❌ Invalid environment variables:\n")
  );

  // Format and display the errors
  const formattedErrors = parsedEnv.error.flatten().fieldErrors;
  for (const key in formattedErrors) {
    const errorMessages = formattedErrors[key as keyof typeof formattedErrors];
    if (errorMessages) {
      console.error(chalk.yellow(` • ${key}:`), chalk.white(errorMessages.join(", ")));
    }
  }

  console.error(chalk.cyan("\n💡 Please check your .env file and ensure all required variables are set correctly."));
  process.exit(1); // Exit with an error code
}

export const env: Env = parsedEnv.data;