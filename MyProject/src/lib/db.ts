import { DataSource } from 'typeorm';
import path from 'path';
import { env } from './env';
import chalk from "chalk";

export const AppDataSource = new DataSource({
    type: "sqlite",
    database: env.DB_NAME,
    synchronize: env.NODE_ENV === "development", // In production, this should be false and migrations should be used
    logging: env.NODE_ENV === "development",
    entities: [
        path.join(import.meta.dirname, '../**/*.entity.{ts,js}'),
    ],
    migrations: [],
    subscribers: [],
});

AppDataSource.initialize().then(() => {
    console.log(chalk.green("Database connection established."));
}).catch((error: Error) => {
    console.error(chalk.red("Database connection failed: ", error));
    process.exit(1)
})