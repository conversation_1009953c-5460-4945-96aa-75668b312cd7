import { Request, Response } from 'express';
import { AppDataSource } from '../lib/db';
import { Product } from '../entities/product.entity';

export interface IProductRepository {
    create(data: any): Promise<Product>;
    findById(id: number): Promise<Product | null>;
    findAll(): Promise<Product[]>;
    update(id: number, data: any): Promise<Product>;
    delete(id: number): Promise<void>;
}

export class ProductRepository implements IProductRepository {
    private repository = AppDataSource.getRepository(Product);

    async create(data: any): Promise<Product> {
        const newProduct = this.repository.create(data);
        return this.repository.save(newProduct);
    }

    async findById(id: number): Promise<Product | null> {
        return this.repository.findOneBy({ id: id as any });
    }

    async findAll(): Promise<Product[]> {
        return this.repository.find();
    }

    async update(id: number, data: any): Promise<Product> {
        await this.repository.update(id as any, data);
        return this.findById(id);
    }

    async delete(id: number): Promise<void> {
        await this.repository.delete(id as any);
    }
}