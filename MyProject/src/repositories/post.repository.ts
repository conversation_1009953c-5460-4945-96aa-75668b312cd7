import { Request, Response } from 'express';
import { AppDataSource } from '../lib/db';
import { Post } from '../entities/post.entity';

export interface IPostRepository {
    create(data: any): Promise<Post>;
    findById(id: number): Promise<Post | null>;
    findAll(): Promise<Post[]>;
    update(id: number, data: any): Promise<Post>;
    delete(id: number): Promise<void>;
}

export class PostRepository implements IPostRepository {
    private repository = AppDataSource.getRepository(Post);

    async create(data: any): Promise<Post> {
        const newPost = this.repository.create(data);
        return await this.repository.save(newPost) as unknown as Post;
    }

    async findById(id: number): Promise<Post | null> {
        return this.repository.findOneBy({ id: id as any });
    }

    async findAll(): Promise<Post[]> {
        return this.repository.find();
    }

    async update(id: number, data: any): Promise<Post> {
        await this.repository.update(id as any, data);
        const updatedPost = await this.findById(id);
        if (!updatedPost) {
            throw new Error(`Post with id ${id} not found`);
        }
        return updatedPost;
    }

    async delete(id: number): Promise<void> {
        await this.repository.delete(id as any);
    }
}