import { Request, Response } from 'express';
import { AppDataSource } from '../lib/db';
import { User } from '../entities/user.entity';

export interface IUserRepository {
    create(data: any): Promise<User>;
    findById(id: number): Promise<User | null>;
    findAll(): Promise<User[]>;
    update(id: number, data: any): Promise<User>;
    delete(id: number): Promise<void>;
}

export class UserRepository implements IUserRepository {
    private repository = AppDataSource.getRepository(User);

    async create(data: any): Promise<User> {
        const newUser = this.repository.create(data);
        return this.repository.save(newUser);
    }

    async findById(id: number): Promise<User | null> {
        return this.repository.findOneBy({ id: id as any });
    }

    async findAll(): Promise<User[]> {
        return this.repository.find();
    }

    async update(id: number, data: any): Promise<User> {
        await this.repository.update(id as any, data);
        return this.findById(id);
    }

    async delete(id: number): Promise<void> {
        await this.repository.delete(id as any);
    }
}