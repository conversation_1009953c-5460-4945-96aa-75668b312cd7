import { Request, Response, NextFunction } from 'express';
import { AppError } from '../errors';

/**
 * Send detailed error response in development environment
 * @param err - The AppError instance
 * @param res - Express response object
 * @returns Response with detailed error information
 */
const sendErrorDev = (err: AppError, res: Response): Response => {
  console.error('ERROR 💥:', err); // Log the full error

  // Send detailed information
  return res.status(err.statusCode).json({
    status: err.status,
    error: {
      // Send structured error details
      name: err.name,
      message: err.message,
      stack: err.stack,
      statusCode: err.statusCode,
      status: err.status,
      isOperational: err.isOperational,
      errorCode: err.errorCode,
    },
    message: err.message,
    errorCode: err.errorCode,
    details: err.details,
  });
};

/**
 * Send concise error response in production environment
 * @param err - The AppError instance
 * @param res - Express response object
 * @returns Response with limited error information for security
 */
const sendErrorProd = (err: AppError, res: Response): Response => {
  // A) Operational, trusted error: send message to client
  if (err.isOperational) {
    return res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
      errorCode: err.errorCode,
      details: err.details,
    });
  }

  // B) Programming or other unknown error: don't leak error details
  // 1) Log error for developers (use a proper logger in real apps)
  console.error('ERROR 💥 (Prod - Non-Operational):', err.message, err.stack);

  // 2) Send generic message to client
  return res.status(500).json({
    status: 'error',
    message: 'Something went very wrong!',
    errorCode: 'INTERNAL_SERVER_ERROR', // Generic code for production
    details: err.details,
  });
};

/**
 * Global error handling middleware.
 * This middleware is responsible for handling errors that are not caught by other middleware or route handlers.
 * It sends an appropriate response based on the environment (development or production).
 * @param err - The error object (can be any type)
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
const globalErrorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const environment = process.env.NODE_ENV || 'development';

  // Ensure the error is an instance of AppError for consistent handling.
  // If it's not, convert it into a generic, non-operational AppError.
  let normalizedError: AppError;

  if (err instanceof AppError) {
    normalizedError = err;
    normalizedError.statusCode = err.statusCode || 500;
    normalizedError.status = err.status || 'error';
  } else if (err instanceof Error) {
    // Handle generic JavaScript Error objects
    console.error('GENERIC ERROR (Converted to AppError):', err);
    normalizedError = new AppError(
      err.message || 'An unexpected error occurred.',
      500,
      false,
      'UNHANDLED_ERROR',
    );
    normalizedError.stack = err.stack; // Preserve original stack
  } else {
    // Handle cases where something other than an Error was thrown
    console.error('UNKNOWN THROWN VALUE (Converted to AppError):', err);
    normalizedError = new AppError(
      'An unknown error occurred.',
      500,
      false,
      'UNKNOWN_THROW',
    );
  }

  if (environment === 'development') {
    sendErrorDev(normalizedError, res);
    return;
  } else if (environment === 'production') {
    const errorToSend = normalizedError; // Start with the normalized error
    sendErrorProd(errorToSend, res);
    return;
  } else {
    console.warn(
      `NODE_ENV (${environment}) is not 'development' or 'production', defaulting to development error handling.`,
    );
    sendErrorDev(normalizedError, res);
    return;
  }
};

export default globalErrorHandler;