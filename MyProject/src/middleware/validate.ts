import { Request, Response, NextFunction } from 'express';
import { ZodObject } from 'zod/v4';
import { ValidationError } from '../errors';

export const validate = (schema: ZodObject<any, any>) =>
  (req: Request, res: Response, next: NextFunction) => {
    const validatedData = schema.safeParse({
      params: req.params,
      query: req.query,
      body: req.body,
    });

    if (validatedData.success) {
      req.body = validatedData.data.body;
      // req.query = validatedData.data.query as Record<string, any>;
      req.params = validatedData.data.params as Record<string, any>;
      next();
      return;
    }

    const error = new ValidationError(
      validatedData.error,
      "Validation Error",
      "VALIDATION_ERROR"
    );
    next(error);
  };