import { Router } from "express";
import { validate } from "../middleware/validate";
import {
    createUser,
    getAllUsers,
    getUser,
    updateUser,
    deleteUser
} from '../controllers/user.controller'

import {
    createUserRequestSchema,
    updateUserRequestSchema,
    deleteUserRequestSchema,
    getUserRequestSchema,
} from "../schemas/user.schema";


const router = Router();

router.post('/', validate(createUserRequestSchema), createUser);
router.get('/', getAllUsers);
router.get('/:id', validate(getUserRequestSchema), getUser);
router.patch('/:id', validate(updateUserRequestSchema), updateUser);
router.delete('/:id', validate(deleteUserRequestSchema), deleteUser);

export default router;