import { Router } from "express";
import { validate } from "../middleware/validate";
import {
    createPost,
    getAllPosts,
    getPost,
    updatePost,
    deletePost
} from '../controllers/post.controller'

import {
    createPostRequestSchema,
    updatePostRequestSchema,
    deletePostRequestSchema,
    getPostRequestSchema,
} from "../schemas/post.schema";


const router = Router();

router.post('/', validate(createPostRequestSchema), createPost);
router.get('/', getAllPosts);
router.get('/:id', validate(getPostRequestSchema), getPost);
router.patch('/:id', validate(updatePostRequestSchema), updatePost);
router.delete('/:id', validate(deletePostRequestSchema), deletePost);

export default router;