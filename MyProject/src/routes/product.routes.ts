import { Router } from "express";
import { validate } from "../middleware/validate";
import {
    createProduct,
    getAllProducts,
    getProduct,
    updateProduct,
    deleteProduct
} from '../controllers/product.controller'

import {
    createProductRequestSchema,
    updateProductRequestSchema,
    deleteProductRequestSchema,
    getProductRequestSchema,
} from "../schemas/product.schema";


const router = Router();

router.post('/', validate(createProductRequestSchema), createProduct);
router.get('/', getAllProducts);
router.get('/:id', validate(getProductRequestSchema), getProduct);
router.patch('/:id', validate(updateProductRequestSchema), updateProduct);
router.delete('/:id', validate(deleteProductRequestSchema), deleteProduct);

export default router;