import { Request, Response } from 'express';
import { ProductRepository } from '../repositories/product.repository';
import { CreateProductDTO, UpdateProductDTO } from "../schemas/product.schema";
import { NotFoundError } from '../errors';


const productRepository = new ProductRepository();

/**
 * Create a new Product
 * @route POST /api/products
 */
export const createProduct = async(req: Request, res: Response) => {
    const data: CreateProductDTO = req.body;
    const newProduct = await productRepository.create(data);
    res.status(201).json(newProduct);
}

/**
 * Get all Products
 * @route GET /api/products
 */
export const getAllProducts = async (req: Request, res: Response) => {
    const products = await productRepository.findAll();
    res.status(200).json(products);
}

/**
 * Get a Product by id
 * @route GET /api/products/:id
 */
export const getProduct = async (req: Request, res: Response) => {
    const { id } = req.params;
    const product = await productRepository.findById(Number(id));
    if (!product) {
        throw new NotFoundError();
    }
    res.status(200).json(product);
}

/**
 * Update a Product by id
 * @route PATCH /api/products/:id
 */
export const updateProduct = async (req: Request, res: Response) => {
    const { id } = req.params;
    const data: UpdateProductDTO = req.body;
    const updatedProduct = await productRepository.update(Number(id), data);
    res.status(200).json(updatedProduct);
}

/**
 * Delete a Product by id
 * @route DELETE /api/products/:id
 */
export const deleteProduct = async (req: Request, res: Response) => {
    const { id } = req.params;
    await productRepository.delete(Number(id));
    res.status(204).send();
}