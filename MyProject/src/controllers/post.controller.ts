import { Request, Response } from 'express';
import { PostRepository } from '../repositories/post.repository';
import { CreatePostDTO, UpdatePostDTO } from "../schemas/post.schema";
import { NotFoundError } from '../errors';


const postRepository = new PostRepository();

/**
 * Create a new Post
 * @route POST /api/posts
 */
export const createPost = async(req: Request, res: Response) => {
    const data: CreatePostDTO = req.body;
    const newPost = await postRepository.create(data);
    res.status(201).json(newPost);
}

/**
 * Get all Posts
 * @route GET /api/posts
 */
export const getAllPosts = async (req: Request, res: Response) => {
    const posts = await postRepository.findAll();
    res.status(200).json(posts);
}

/**
 * Get a Post by id
 * @route GET /api/posts/:id
 */
export const getPost = async (req: Request, res: Response) => {
    const { id } = req.params;
    const post = await postRepository.findById(Number(id));
    if (!post) {
        throw new NotFoundError();
    }
    res.status(200).json(post);
}

/**
 * Update a Post by id
 * @route PATCH /api/posts/:id
 */
export const updatePost = async (req: Request, res: Response) => {
    const { id } = req.params;
    const data: UpdatePostDTO = req.body;
    const updatedPost = await postRepository.update(Number(id), data);
    res.status(200).json(updatedPost);
}

/**
 * Delete a Post by id
 * @route DELETE /api/posts/:id
 */
export const deletePost = async (req: Request, res: Response) => {
    const { id } = req.params;
    await postRepository.delete(Number(id));
    res.status(204).send();
}