import { Request, Response } from 'express';
import { UserRepository } from '../repositories/user.repository';
import { CreateUserDTO, UpdateUserDTO } from "../schemas/user.schema";
import { NotFoundError } from '../errors';


const userRepository = new UserRepository();

/**
 * Create a new User
 * @route POST /api/users
 */
export const createUser = async(req: Request, res: Response) => {
    const data: CreateUserDTO = req.body;
    const newUser = await userRepository.create(data);
    res.status(201).json(newUser);
}

/**
 * Get all Users
 * @route GET /api/users
 */
export const getAllUsers = async (req: Request, res: Response) => {
    const users = await userRepository.findAll();
    res.status(200).json(users);
}

/**
 * Get a User by id
 * @route GET /api/users/:id
 */
export const getUser = async (req: Request, res: Response) => {
    const { id } = req.params;
    const user = await userRepository.findById(Number(id));
    if (!user) {
        throw new NotFoundError();
    }
    res.status(200).json(user);
}

/**
 * Update a User by id
 * @route PATCH /api/users/:id
 */
export const updateUser = async (req: Request, res: Response) => {
    const { id } = req.params;
    const data: UpdateUserDTO = req.body;
    const updatedUser = await userRepository.update(Number(id), data);
    res.status(200).json(updatedUser);
}

/**
 * Delete a User by id
 * @route DELETE /api/users/:id
 */
export const deleteUser = async (req: Request, res: Response) => {
    const { id } = req.params;
    await userRepository.delete(Number(id));
    res.status(204).send();
}