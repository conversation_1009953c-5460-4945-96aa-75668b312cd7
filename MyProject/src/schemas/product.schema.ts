import { z } from "zod/v4";

export const createProductRequestSchema = z.object({
  body: z.object({
    id: z.number(),
    name: z.string().min(3).max(200),
    price: z.number().min(0.01),
    description: z.string().optional(),
    category: z.enum(['Electronics', 'Books', 'Clothing', 'Home']),
    sku: z.string().uuid()
  }),
});

export const updateProductRequestSchema = z.object({
  body: z.object({
    id: z.number(),
    name: z.string().min(3).max(200),
    price: z.number().min(0.01),
    description: z.string().optional(),
    category: z.enum(['Electronics', 'Books', 'Clothing', 'Home']),
    sku: z.string().uuid()
  }).partial(),
  params: z.object({
    id: z.number().int(),
  }),
});

export const deleteProductRequestSchema = z.object({
  params: z.object({
    id: z.number().int(),
  }),
});

export const getProductRequestSchema = z.object({
  params: z.object({
    id: z.number().int(),
  }),
});

export type CreateProductDTO = z.infer<typeof createProductRequestSchema>["body"];
export type UpdateProductDTO = z.infer<typeof updateProductRequestSchema>["body"];