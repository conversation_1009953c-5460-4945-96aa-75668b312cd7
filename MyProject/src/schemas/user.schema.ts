import { z } from "zod/v4";

export const createUserRequestSchema = z.object({
  body: z.object({
    id: z.number(),
    email: z.string().email(),
    password: z.string().min(8).max(255),
    createdAt: z.date().default(() => new Date()),
    updatedAt: z.date().default(() => new Date()),
    posts: z.number().array().optional()
  }),
});

export const updateUserRequestSchema = z.object({
  body: z.object({
    id: z.number(),
    email: z.string().email(),
    password: z.string().min(8).max(255),
    createdAt: z.date().default(() => new Date()),
    updatedAt: z.date().default(() => new Date()),
    posts: z.number().array().optional()
  }).partial(),
  params: z.object({
    id: z.number().int(),
  }),
});

export const deleteUserRequestSchema = z.object({
  params: z.object({
    id: z.number().int(),
  }),
});

export const getUserRequestSchema = z.object({
  params: z.object({
    id: z.number().int(),
  }),
});

export type CreateUserDTO = z.infer<typeof createUserRequestSchema>["body"];
export type UpdateUserDTO = z.infer<typeof updateUserRequestSchema>["body"];