import { z } from "zod/v4";

export const createPostRequestSchema = z.object({
  body: z.object({
    id: z.number(),
    title: z.string().min(5).max(100),
    content: z.string().optional(),
    published: z.boolean().default(false),
    author: z.number(),
    createdAt: z.date().default(() => new Date())
  }),
});

export const updatePostRequestSchema = z.object({
  body: z.object({
    id: z.number(),
    title: z.string().min(5).max(100),
    content: z.string().optional(),
    published: z.boolean().default(false),
    author: z.number(),
    createdAt: z.date().default(() => new Date())
  }).partial(),
  params: z.object({
    id: z.number().int(),
  }),
});

export const deletePostRequestSchema = z.object({
  params: z.object({
    id: z.number().int(),
  }),
});

export const getPostRequestSchema = z.object({
  params: z.object({
    id: z.number().int(),
  }),
});

export type CreatePostDTO = z.infer<typeof createPostRequestSchema>["body"];
export type UpdatePostDTO = z.infer<typeof updatePostRequestSchema>["body"];