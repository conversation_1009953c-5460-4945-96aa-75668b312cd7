import { Column, <PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('posts')
export class Post {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ nullable: false, length: 100 })
    title: string;

    @Column({ nullable: true })
    content: string | null;

    @Column({ nullable: false })
    published: boolean;

    @ManyToOne(() => User, (user: User) => user.posts)
    author: User;

    @Column({ nullable: false, default: () => "CURRENT_TIMESTAMP" })
    createdAt: Date;
}
