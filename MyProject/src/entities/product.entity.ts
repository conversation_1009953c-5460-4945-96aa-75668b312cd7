import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum Category {
    Electronics = 'Electronics',
    Books = 'Books',
    Clothing = 'Clothing',
    Home = 'Home'
}


@Entity('products')
export class Product {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ nullable: false, unique: true, length: 200 })
    name: string;

    @Column({ nullable: false })
    price: number;

    @Column({ nullable: true })
    description: string | null;

    @Column({ type: "enum", enum: Category, nullable: false })
    category: Category;

    @Column({ nullable: false, unique: true, default: () => "UUID()" })
    sku: string;
}
