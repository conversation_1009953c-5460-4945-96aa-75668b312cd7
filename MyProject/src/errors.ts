import {ZodError} from "zod/v4";

export class AppError extends Error {
    public statusCode: number;
    public status: 'fail' | 'error';
    public isOperational: boolean;
    public errorCode: string;
    public details: unknown;

    /**
     * @param message - Error message for the client
     * @param statusCode - HTTP status code (e.g., 400, 404, 500)
     * @param isOperational - True for expected errors (e.g., user input), false for bugs
     * @param errorCode - Optional unique code for the error type
     * @param details - Optional additional details about the error
     */
    constructor(
        message: string,
        statusCode: number,
        isOperational: boolean = true,
        errorCode: string = 'GENERIC_ERROR',
        details: unknown = undefined
    ) {
        super(message);

        this.statusCode = statusCode;
        this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
        this.isOperational = isOperational;
        this.errorCode = errorCode;
        this.details = details;

        // Capture the stack trace, excluding the constructor call from it
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        } else {
            // Fallback if captureStackTrace is not available
            this.stack = new Error(message).stack;
        }

        // Set the prototype explicitly (required for extending built-in classes like Error in TS)
        Object.setPrototypeOf(this, AppError.prototype);
    }
}

export class NotFoundError extends AppError {
    constructor(
        message: string = "Not Found",
    ) {
        super(message, 404, true, "NOT_FOUND");
    }
}

export class InternalServerError extends AppError {
    constructor(
        message: string = "Internal Server Error",
    ) {
        super(message, 500, false, "INTERNAL_SERVER_ERROR");
    }
}

export class BadRequestError extends AppError {
    constructor(
        message: string = "Bad Request",
    ) {
        super(message, 400, true, "BAD_REQUEST");
    }
}

export class UnauthorizedError extends AppError {
    constructor(
        message: string = "Unauthorized access",
        errorCode: string = "UNAUTHORIZED"
    ) {
        super(message, 401, true, errorCode);
    }
}

export class ForbiddenError extends AppError {
    constructor(
        message: string = "Access forbidden",
        errorCode: string = "FORBIDDEN",
    ) {
        super(message, 403, true, errorCode);
    }
}

export class ValidationError extends AppError {
    constructor(
        zodError: ZodError,
        message: string = "Validation Error",
        errorCode: string = "VALIDATION_ERROR",
    ) {
        super(message, 400, true, errorCode, zodError.issues);
    }
}